#!/bin/bash

# PXPAC链视频内容上链脚本 (简化版)
# 调用新实现的后端directBlockchainUpload接口进行真实上链

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $CYAN "🎬 PXPAC链原创视频内容上链脚本"
print_message $CYAN "======================================="
print_message $BLUE "📹 内容类型: 原创视频内容"
print_message $BLUE "🔗 目标链: PXPAC Production Chain"
print_message $BLUE "💰 支付代币: PXPAC-T (wPAT)"
print_message $BLUE "📦 存储: Pinata IPFS"
print_message $BLUE "🚀 接口: directBlockchainUpload"
print_message $CYAN "======================================="

# 配置
# 线上环境 (注释掉用于测试)
# BACKEND_URL="https://testbackend.pxpac.com"

# 本地测试环境
BACKEND_URL="http://localhost:8080"

# 内嵌视频数据
create_video_data() {
    cat > /tmp/video_data.json << 'EOF'
{
    "content_ksuid": "LwOT6gPHSv8OG6zfb3O3mUwcwR",
    "creator_ksuid": "2ztzcl8eDbf5jnsTc2M6bRnag2L",
    "title": "PXPAC区块链技术深度解析",
    "description": "本视频深入介绍PXPAC区块链的核心技术架构，包括共识机制、智能合约、跨链桥接等关键技术。适合区块链开发者和技术爱好者学习。内容涵盖：1. PXPAC链的技术特点 2. 智能合约开发实践 3. 跨链技术应用 4. 生态系统建设",
    "type": "video",
    "status": "published",
    "rating": "A",
    "video_ID": "pxpac_tech_001",
    "user_ksuid": "2ztzcl8eDbf5jnsTc2M6bRnag2L",
    "cover": "pxpac_blockchain_tech_cover.jpg",
    "cover_url": "https://pxpac.com/content/covers/blockchain_tech_analysis.jpg",
    "play_url": "{\"1080p\":{\"path\":\"https://pxpac.com/content/videos/pxpac_tech_analysis_1080p.mp4\",\"width\":1920,\"height\":1080},\"720p\":{\"path\":\"https://pxpac.com/content/videos/pxpac_tech_analysis_720p.mp4\",\"width\":1280,\"height\":720}}",
    "preview_url": "https://pxpac.com/content/previews/pxpac_tech_analysis_preview.mp4",
    "duration": 1245.8,
    "language": "zh-CN",
    "orientation": "landscape",
    "creation_time": "2025-07-24T10:00:00+08:00",
    "view_count": 0,
    "like_count": 0,
    "dislike_count": 0,
    "comment_count": 0,
    "share_count": 0,
    "favorite_count": 0,
    "original_tags": "PXPAC,区块链,技术解析,智能合约,跨链技术,去中心化",
    "category": {
        "id": 80,
        "name": "科技 / 教育",
        "slug": "keji-jiaoyu"
    },
    "has_collaborators": false,
    "all_approved": true,
    "content_source": "original",
    "is_original": true,
    "sharing_type": "original_creation",
    "source_platform": null,
    "source_url": null,
    "original_creator": "PXPAC技术团队",
    "copyright_status": "original",
    "audit_info": {
        "status": "permit",
        "votes": 10,
        "permit_num": 10,
        "reject_num": 0,
        "auditor_logs": [
            {
                "user_ksuid": "2zMBqxYbpYkWIqRSYsN5Tojc7Xn",
                "vote_type": "permit",
                "vote_weight": 10,
                "reason": "技术内容专业准确，教育价值高，建议通过审核。",
                "created_at": "2025-07-24T10:30:00Z"
            },
            {
                "user_ksuid": "2zMBqxYbsN5Tojc7XnpYkWIqRSY",
                "vote_type": "permit",
                "vote_weight": 9,
                "reason": "区块链技术讲解清晰，适合技术学习，推荐通过。",
                "created_at": "2025-07-24T11:15:00Z"
            },
            {
                "user_ksuid": "2SYsN5Tojc7XnzMBqxYbpYkWIqR",
                "vote_type": "permit",
                "vote_weight": 10,
                "reason": "原创技术内容，制作精良，对生态发展有益，同意发布。",
                "created_at": "2025-07-24T12:00:00Z"
            }
        ]
    },
    "collaborators": [
        {
            "user_ksuid": "2zax42xzaBVPXdhFJ2PQMJ8ixr9",
            "role_name": "技术讲师",
            "user_info": {
                "username": "李技术",
                "nickname": "区块链架构师"
            }
        },
        {
            "user_ksuid": "2zaPBVPXdhFJ2J8ixrQMx42xza9",
            "role_name": "内容策划",
            "user_info": {
                "username": "王策划",
                "nickname": "技术内容专家"
            }
        },
        {
            "user_ksuid": "2zaPQMx42xzaBVPXdhFJ2J8ixr9",
            "role_name": "视频制作",
            "user_info": {
                "username": "张制作",
                "nickname": "多媒体制作师"
            }
        }
    ]
}
EOF
}

# 清理函数
cleanup() {
    if [ -f "/tmp/video_data.json" ]; then
        rm -f /tmp/video_data.json
    fi
}
trap cleanup EXIT

# 检查依赖
check_dependencies() {
    print_message $BLUE "🔍 检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        print_message $RED "❌ curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_message $RED "❌ jq 未安装，请运行: brew install jq"
        exit 1
    fi
    
    print_message $GREEN "✅ 依赖检查通过"
}

# 检查后端服务
check_backend() {
    print_message $BLUE "🔍 检查后端服务..."

    if ! curl -s "${BACKEND_URL}/health" > /dev/null 2>&1; then
        print_message $RED "❌ 后端服务不可用: ${BACKEND_URL}"
        print_message $YELLOW "💡 请确保后端服务已启动:"
        print_message $YELLOW "   cd pxpac-scan/backend && go run cmd/main.go"
        exit 1
    fi

    print_message $GREEN "✅ 后端服务正常运行"

    # 提示重启后端服务
    print_message $YELLOW "💡 如果遇到IPFS配置错误，请重启后端服务:"
    print_message $YELLOW "   1. 停止当前后端服务 (Ctrl+C)"
    print_message $YELLOW "   2. 重新启动: cd pxpac-scan/backend && go run cmd/main.go"
}

# 上链视频内容
upload_video() {
    print_message $BLUE "📤 开始上链视频内容..."
    
    # 创建视频数据
    create_video_data
    VIDEO_DATA_FILE="/tmp/video_data.json"
    
    # 提取基本信息
    local title=$(jq -r '.title' $VIDEO_DATA_FILE)
    local description=$(jq -r '.description' $VIDEO_DATA_FILE)
    local content_type=$(jq -r '.type' $VIDEO_DATA_FILE)
    local content_ksuid=$(jq -r '.content_ksuid' $VIDEO_DATA_FILE)
    local user_ksuid=$(jq -r '.user_ksuid' $VIDEO_DATA_FILE)
    
    print_message $YELLOW "📋 视频信息:"
    print_message $YELLOW "   标题: $title"
    print_message $YELLOW "   内容ID: $content_ksuid"
    print_message $YELLOW "   创建者: $user_ksuid"
    print_message $YELLOW "   类型: $content_type"
    
    # 构建完整的元数据
    local metadata=$(jq -c '{
        content_ksuid: .content_ksuid,
        user_ksuid: .user_ksuid,
        video_ID: .video_ID,
        duration: .duration,
        language: .language,
        orientation: .orientation,
        category: .category,
        rating: .rating,
        view_count: .view_count,
        like_count: .like_count,
        dislike_count: .dislike_count,
        comment_count: .comment_count,
        share_count: .share_count,
        favorite_count: .favorite_count,
        cover: .cover,
        cover_url: .cover_url,
        play_url: .play_url,
        preview_url: .preview_url,
        creation_time: .creation_time,
        original_tags: .original_tags,
        audit_info: .audit_info,
        has_collaborators: .has_collaborators,
        all_approved: .all_approved,
        content_source: .content_source,
        is_original: .is_original,
        sharing_type: .sharing_type,
        source_platform: .source_platform,
        source_url: .source_url,
        original_creator: .original_creator,
        copyright_status: .copyright_status,
        upload_timestamp: "'"$(date -u +%Y-%m-%dT%H:%M:%SZ)"'",
        upload_source: "pxpac_chain_script"
    }' $VIDEO_DATA_FILE)
    
    # 将视频数据转换为base64编码
    local file_data_base64=$(cat $VIDEO_DATA_FILE | base64 | tr -d '\n')

    # 构建API请求数据 - 适配新的directBlockchainUpload接口
    local request_data=$(cat <<EOF
{
    "content_ksuid": "$content_ksuid",
    "creator_ksuid": "$user_ksuid",
    "title": "$title",
    "description": "$description",
    "content_type": "$content_type",
    "file_data": "$file_data_base64",
    "file_name": "pxpac_video_${content_ksuid}.json",
    "metadata": $metadata
}
EOF
    )
    
    print_message $BLUE "📤 发送上链请求到后端API..."
    print_message $YELLOW "🔍 调试: 请求URL: ${BACKEND_URL}/api/v1/content/real-upload"

    # 发送API请求
    local response=$(curl -s -w "\n%{http_code}" \
        -X POST "${BACKEND_URL}/api/v1/content/real-upload" \
        -H "Content-Type: application/json" \
        -d "$request_data")

    local http_code=$(echo "$response" | tail -n1)
    local response_body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "202" ]; then
        print_message $GREEN "🎉 异步上链任务创建成功!"

        # 解析异步响应
        local success=$(echo "$response_body" | jq -r '.success // false')
        local task_id=$(echo "$response_body" | jq -r '.task_id // "N/A"')
        local content_ksuid=$(echo "$response_body" | jq -r '.content_ksuid // "N/A"')
        local status=$(echo "$response_body" | jq -r '.status // "N/A"')
        local message=$(echo "$response_body" | jq -r '.message // "N/A"')
        local estimated_time=$(echo "$response_body" | jq -r '.estimated_time // "N/A"')
        
        # 显示异步任务详情
        echo ""
        print_message $CYAN "🎬 异步上链任务详情:"
        print_message $CYAN "=================================="
        print_message $GREEN "📹 视频标题: $title"
        print_message $GREEN "🆔 任务ID: $task_id"
        print_message $GREEN "🔑 内容KSUID: $content_ksuid"
        print_message $GREEN "📊 当前状态: $status"
        print_message $GREEN "⏱️ 预计时间: $estimated_time"
        print_message $GREEN "� 状态消息: $message"
        
        echo ""
        print_message $PURPLE "🔗 查询命令:"
        print_message $PURPLE "   查询任务状态: ./simple-upload-original-video.sh --check-task $task_id"
        print_message $PURPLE "   查询内容状态: ./simple-upload-original-video.sh --check-content $content_ksuid"

        echo ""
        print_message $PURPLE "🌐 API查询链接:"
        print_message $PURPLE "   任务状态: ${BACKEND_URL}/api/v1/tasks/$task_id"
        print_message $PURPLE "   内容状态: ${BACKEND_URL}/api/v1/tasks/content/$content_ksuid"

        echo ""
        print_message $GREEN "✅ 异步上链任务已创建! 正在后台处理中..."
        print_message $YELLOW "💡 请使用上述命令查询处理进度"
        
        return 0
    else
        print_message $RED "❌ 视频上链失败 (HTTP $http_code)"

        # 尝试解析错误信息
        local error_message=$(echo "$response_body" | jq -r '.message // .error // "Unknown error"' 2>/dev/null)

        if echo "$error_message" | grep -q "尚未实现\|not implemented"; then
            print_message $YELLOW "⚠️ 这是预期的错误：区块链上链功能尚未完全实现"
            print_message $BLUE "💡 说明："
            print_message $BLUE "   - 后端接口已经创建，但区块链调用部分还需要完善"
            print_message $BLUE "   - IPFS上传功能应该正常工作"
            print_message $BLUE "   - 这表明新的directBlockchainUpload接口正在正常运行"
        else
            print_message $YELLOW "🔍 错误信息: $error_message"
        fi

        print_message $YELLOW "🔍 调试: 响应内容:"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"

        return 1
    fi
}

# 主函数
main() {
    check_dependencies
    check_backend

    print_message $BLUE "🚀 开始自动上链视频内容..."

    if upload_video; then
        echo ""
        print_message $GREEN "🎊 视频内容上链流程完成!"
    else
        print_message $RED "❌ 视频内容上链失败"
        exit 1
    fi
}

# 任务状态查询函数
check_task_status() {
    local task_id="$1"
    local content_ksuid="$2"

    if [ -n "$task_id" ]; then
        echo ""
        print_message $CYAN "🔍 查询任务状态..."
        print_message $YELLOW "任务ID: $task_id"

        local status_response=$(curl -s "${BACKEND_URL}/api/v1/tasks/${task_id}")
        echo ""
        print_message $BLUE "📋 任务状态响应:"
        echo "$status_response" | jq '.' 2>/dev/null || echo "$status_response"

    elif [ -n "$content_ksuid" ]; then
        echo ""
        print_message $CYAN "🔍 根据内容ID查询任务状态..."
        print_message $YELLOW "内容KSUID: $content_ksuid"

        local status_response=$(curl -s "${BACKEND_URL}/api/v1/tasks/content/${content_ksuid}")
        echo ""
        print_message $BLUE "📋 任务状态响应:"
        echo "$status_response" | jq '.' 2>/dev/null || echo "$status_response"
    fi
}

# 使用说明函数
show_usage() {
    echo ""
    print_message $CYAN "📖 使用说明:"
    print_message $WHITE "1. 上链视频内容:"
    print_message $WHITE "   ./simple-upload-original-video.sh"
    print_message $WHITE ""
    print_message $WHITE "2. 查询任务状态 (通过任务ID):"
    print_message $WHITE "   ./simple-upload-original-video.sh --check-task <task_id>"
    print_message $WHITE ""
    print_message $WHITE "3. 查询任务状态 (通过内容KSUID):"
    print_message $WHITE "   ./simple-upload-original-video.sh --check-content <content_ksuid>"
    print_message $WHITE ""
    print_message $WHITE "💡 提示: 本地测试环境 - ${BACKEND_URL}"
    print_message $WHITE "💡 切换到线上: 取消注释线上URL，注释本地URL"
}

# 处理命令行参数
if [ "$1" = "--check-task" ] && [ -n "$2" ]; then
    check_task_status "$2" ""
    exit 0
elif [ "$1" = "--check-content" ] && [ -n "$2" ]; then
    check_task_status "" "$2"
    exit 0
elif [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_usage
    exit 0
elif [ $# -gt 0 ]; then
    print_message $RED "❌ 未知参数: $1"
    show_usage
    exit 1
fi

# 运行主函数
main "$@"
